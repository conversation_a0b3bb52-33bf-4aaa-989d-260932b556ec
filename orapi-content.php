<?php
/**
 * Plugin Name: OpenRouter API Content Generator
 * Plugin URI: https://example.com
 * Description: Generate WordPress post content using OpenRouter API with bulk processing capabilities
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: orapi-content
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('ORAPI_CONTENT_VERSION', '1.0.0');
define('ORAPI_CONTENT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ORAPI_CONTENT_PLUGIN_URL', plugin_dir_url(__FILE__));

// Main plugin class
class OrapiContentGenerator {
    
    private $option_name = 'orapi_content_settings';
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'settings_init'));
        add_action('wp_ajax_orapi_generate_content', array($this, 'ajax_generate_content'));
        add_action('wp_ajax_orapi_bulk_generate', array($this, 'ajax_bulk_generate'));

        // Add meta box to post edit screen
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        
        // Security: Add nonce verification
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
    }
    
    public function init() {
        // Initialize plugin
        load_plugin_textdomain('orapi-content', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function add_admin_menu() {
        add_options_page(
            __('OpenRouter API Content', 'orapi-content'),
            __('OpenRouter API', 'orapi-content'),
            'manage_options',
            'orapi-content',
            array($this, 'options_page')
        );
        
        add_management_page(
            __('Bulk Content Generator', 'orapi-content'),
            __('Bulk Content Generator', 'orapi-content'),
            'manage_options',
            'orapi-bulk-content',
            array($this, 'bulk_page')
        );
    }
    
    public function settings_init() {
        register_setting('orapi_content', $this->option_name, array($this, 'sanitize_settings'));
        
        add_settings_section(
            'orapi_content_section',
            __('OpenRouter API Settings', 'orapi-content'),
            array($this, 'settings_section_callback'),
            'orapi_content'
        );
        
        add_settings_field(
            'api_key',
            __('API Key', 'orapi-content'),
            array($this, 'api_key_render'),
            'orapi_content',
            'orapi_content_section'
        );
        
        add_settings_field(
            'model',
            __('Model', 'orapi-content'),
            array($this, 'model_render'),
            'orapi_content',
            'orapi_content_section'
        );
        
        add_settings_field(
            'prompt',
            __('Default Prompt Template', 'orapi-content'),
            array($this, 'prompt_render'),
            'orapi_content',
            'orapi_content_section'
        );
        
        add_settings_field(
            'wait_time',
            __('API Wait Time (seconds)', 'orapi-content'),
            array($this, 'wait_time_render'),
            'orapi_content',
            'orapi_content_section'
        );
    }
    
    public function sanitize_settings($input) {
        $sanitized = array();
        
        // Sanitize API key - encrypt it
        if (isset($input['api_key'])) {
            $sanitized['api_key'] = $this->encrypt_api_key(sanitize_text_field($input['api_key']));
        }
        
        // Sanitize model
        if (isset($input['model'])) {
            $sanitized['model'] = sanitize_text_field($input['model']);
        }
        
        // Sanitize prompt
        if (isset($input['prompt'])) {
            $sanitized['prompt'] = wp_kses_post($input['prompt']);
        }
        
        // Sanitize wait time
        if (isset($input['wait_time'])) {
            $sanitized['wait_time'] = absint($input['wait_time']);
            if ($sanitized['wait_time'] < 1) {
                $sanitized['wait_time'] = 1;
            }
        }
        
        return $sanitized;
    }
    
    private function encrypt_api_key($api_key) {
        if (empty($api_key)) {
            return '';
        }
        
        // Use WordPress salts for encryption
        $key = wp_salt('auth');
        $iv = substr(hash('sha256', wp_salt('secure_auth')), 0, 16);
        
        return base64_encode(openssl_encrypt($api_key, 'AES-256-CBC', $key, 0, $iv));
    }
    
    private function decrypt_api_key($encrypted_key) {
        if (empty($encrypted_key)) {
            return '';
        }
        
        $key = wp_salt('auth');
        $iv = substr(hash('sha256', wp_salt('secure_auth')), 0, 16);
        
        return openssl_decrypt(base64_decode($encrypted_key), 'AES-256-CBC', $key, 0, $iv);
    }
    
    public function settings_section_callback() {
        echo '<p>' . __('Configure your OpenRouter API settings below. The API key will be encrypted for security.', 'orapi-content') . '</p>';
    }
    
    public function api_key_render() {
        $options = get_option($this->option_name);
        $api_key = isset($options['api_key']) ? $this->decrypt_api_key($options['api_key']) : '';
        
        echo '<input type="password" name="' . $this->option_name . '[api_key]" value="' . esc_attr($api_key) . '" class="regular-text" />';
        echo '<p class="description">' . __('Your OpenRouter API key (will be encrypted when saved)', 'orapi-content') . '</p>';
    }
    
    public function model_render() {
        $options = get_option($this->option_name);
        $model = isset($options['model']) ? $options['model'] : 'openai/gpt-3.5-turbo';
        
        echo '<input type="text" name="' . $this->option_name . '[model]" value="' . esc_attr($model) . '" class="regular-text" />';
        echo '<p class="description">' . __('OpenRouter model to use (e.g., openai/gpt-3.5-turbo, anthropic/claude-3-haiku)', 'orapi-content') . '</p>';
    }
    
    public function prompt_render() {
        $options = get_option($this->option_name);
        $prompt = isset($options['prompt']) ? $options['prompt'] : 'Write a comprehensive blog post about {{post_title}}. Make it engaging and informative.';
        
        echo '<textarea name="' . $this->option_name . '[prompt]" rows="5" cols="50" class="large-text">' . esc_textarea($prompt) . '</textarea>';
        echo '<p class="description">' . __('Use {{post_title}} as a placeholder for the post title', 'orapi-content') . '</p>';
    }
    
    public function wait_time_render() {
        $options = get_option($this->option_name);
        $wait_time = isset($options['wait_time']) ? $options['wait_time'] : 2;
        
        echo '<input type="number" name="' . $this->option_name . '[wait_time]" value="' . esc_attr($wait_time) . '" min="1" max="60" />';
        echo '<p class="description">' . __('Wait time between API calls to respect rate limits', 'orapi-content') . '</p>';
    }
    
    public function options_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form action="options.php" method="post">
                <?php
                settings_fields('orapi_content');
                do_settings_sections('orapi_content');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }
    
    public function admin_enqueue_scripts($hook) {
        // Enqueue CSS for both settings and bulk pages
        if ($hook === 'tools_page_orapi-bulk-content' || $hook === 'settings_page_orapi-content') {
            wp_enqueue_style('orapi-admin-css', ORAPI_CONTENT_PLUGIN_URL . 'assets/admin.css', array(), ORAPI_CONTENT_VERSION);
        }

        // Enqueue JS only for bulk page
        if ($hook === 'tools_page_orapi-bulk-content') {
            wp_enqueue_script('orapi-bulk-js', ORAPI_CONTENT_PLUGIN_URL . 'assets/bulk.js', array('jquery'), ORAPI_CONTENT_VERSION, true);
            wp_localize_script('orapi-bulk-js', 'orapi_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('orapi_bulk_nonce'),
                'strings' => array(
                    'processing' => __('Processing...', 'orapi-content'),
                    'completed' => __('Completed', 'orapi-content'),
                    'error' => __('Error', 'orapi-content')
                )
            ));
        }
    }

    public function bulk_page() {
        $posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'draft',
            'numberposts' => -1,
            'meta_query' => array(
                array(
                    'key' => '_orapi_generated',
                    'compare' => 'NOT EXISTS'
                )
            )
        ));

        $options = get_option($this->option_name);
        $wait_time = isset($options['wait_time']) ? $options['wait_time'] : 2;

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div id="orapi-bulk-container">
                <div class="orapi-bulk-controls">
                    <h3><?php _e('Bulk Content Generation', 'orapi-content'); ?></h3>
                    <p><?php _e('Generate content for draft posts that don\'t have AI-generated content yet.', 'orapi-content'); ?></p>

                    <div class="orapi-settings-info">
                        <p><strong><?php _e('Current Settings:', 'orapi-content'); ?></strong></p>
                        <ul>
                            <li><?php _e('Model:', 'orapi-content'); ?> <?php echo esc_html($options['model'] ?? 'Not configured'); ?></li>
                            <li><?php _e('Wait Time:', 'orapi-content'); ?> <?php echo esc_html($wait_time); ?> <?php _e('seconds', 'orapi-content'); ?></li>
                            <li><?php _e('API Key:', 'orapi-content'); ?> <?php echo !empty($options['api_key']) ? __('Configured', 'orapi-content') : __('Not configured', 'orapi-content'); ?></li>
                        </ul>
                        <?php if (empty($options['api_key'])): ?>
                            <div class="notice notice-error">
                                <p><?php _e('Please configure your API settings before running bulk generation.', 'orapi-content'); ?>
                                <a href="<?php echo admin_url('options-general.php?page=orapi-content'); ?>"><?php _e('Go to Settings', 'orapi-content'); ?></a></p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="orapi-custom-prompt">
                        <label for="custom-prompt"><?php _e('Custom Prompt (optional):', 'orapi-content'); ?></label>
                        <textarea id="custom-prompt" rows="3" cols="50" class="large-text" placeholder="<?php _e('Leave empty to use default prompt from settings. Use {{post_title}} for post title.', 'orapi-content'); ?>"></textarea>
                    </div>

                    <input type="hidden" id="wait-time-setting" value="<?php echo esc_attr($wait_time); ?>" />

                    <button id="start-bulk-generation" class="button button-primary" <?php echo (empty($posts) || empty($options['api_key'])) ? 'disabled' : ''; ?>>
                        <?php _e('Start Bulk Generation', 'orapi-content'); ?>
                    </button>

                    <button id="stop-bulk-generation" class="button" style="display: none;">
                        <?php _e('Stop Generation', 'orapi-content'); ?>
                    </button>
                </div>

                <div class="orapi-progress" style="display: none;">
                    <h4><?php _e('Progress', 'orapi-content'); ?></h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <p class="progress-text">0 / <?php echo count($posts); ?> posts processed</p>
                </div>

                <div class="orapi-posts-list">
                    <h4><?php _e('Posts to Process', 'orapi-content'); ?> (<?php echo count($posts); ?>)</h4>

                    <?php if (empty($posts)): ?>
                        <p><?php _e('No draft posts found that need content generation.', 'orapi-content'); ?></p>
                    <?php else: ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Title', 'orapi-content'); ?></th>
                                    <th><?php _e('Date', 'orapi-content'); ?></th>
                                    <th><?php _e('Status', 'orapi-content'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($posts as $post): ?>
                                    <tr data-post-id="<?php echo $post->ID; ?>">
                                        <td>
                                            <strong><?php echo esc_html($post->post_title); ?></strong>
                                        </td>
                                        <td><?php echo get_the_date('Y-m-d H:i', $post); ?></td>
                                        <td class="status-cell">
                                            <span class="status-pending"><?php _e('Pending', 'orapi-content'); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
    }

    public function add_meta_boxes() {
        add_meta_box(
            'orapi-content-generator',
            __('OpenRouter Content Generator', 'orapi-content'),
            array($this, 'meta_box_callback'),
            'post',
            'side',
            'default'
        );
    }

    public function meta_box_callback($post) {
        // Check if content was already generated
        $generated = get_post_meta($post->ID, '_orapi_generated', true);
        $options = get_option($this->option_name);

        wp_nonce_field('orapi_generate_single', 'orapi_single_nonce');

        ?>
        <div class="orapi-meta-box">
            <?php if ($generated): ?>
                <p><strong><?php _e('Content Generated:', 'orapi-content'); ?></strong><br>
                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($generated))); ?></p>
            <?php endif; ?>

            <div class="orapi-single-prompt">
                <label for="orapi-single-prompt"><?php _e('Custom Prompt:', 'orapi-content'); ?></label>
                <textarea id="orapi-single-prompt" rows="3" style="width: 100%;" placeholder="<?php _e('Leave empty to use default prompt. Use {{post_title}} for post title.', 'orapi-content'); ?>"></textarea>
            </div>

            <p>
                <button type="button" id="orapi-generate-single" class="button button-primary" <?php echo empty($options['api_key']) ? 'disabled' : ''; ?>>
                    <?php _e('Generate Content', 'orapi-content'); ?>
                </button>
            </p>

            <?php if (empty($options['api_key'])): ?>
                <p class="description" style="color: #dc3232;">
                    <?php _e('Please configure your API settings first.', 'orapi-content'); ?>
                    <a href="<?php echo admin_url('options-general.php?page=orapi-content'); ?>"><?php _e('Settings', 'orapi-content'); ?></a>
                </p>
            <?php endif; ?>

            <div id="orapi-single-status" style="margin-top: 10px;"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#orapi-generate-single').on('click', function() {
                var button = $(this);
                var status = $('#orapi-single-status');
                var prompt = $('#orapi-single-prompt').val();

                button.prop('disabled', true).text('<?php _e('Generating...', 'orapi-content'); ?>');
                status.html('<span style="color: #0073aa;"><?php _e('Generating content...', 'orapi-content'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'orapi_generate_content',
                        post_id: <?php echo $post->ID; ?>,
                        custom_prompt: prompt,
                        nonce: $('#orapi_single_nonce').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            status.html('<span style="color: #46b450;"><?php _e('Content generated successfully!', 'orapi-content'); ?></span>');
                            // Reload the page to show updated content
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            status.html('<span style="color: #dc3232;"><?php _e('Error:', 'orapi-content'); ?> ' + response.data + '</span>');
                            button.prop('disabled', false).text('<?php _e('Generate Content', 'orapi-content'); ?>');
                        }
                    },
                    error: function() {
                        status.html('<span style="color: #dc3232;"><?php _e('An error occurred. Please try again.', 'orapi-content'); ?></span>');
                        button.prop('disabled', false).text('<?php _e('Generate Content', 'orapi-content'); ?>');
                    }
                });
            });
        });
        </script>
        <?php
    }

    public function ajax_generate_content() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'orapi_generate_single')) {
            wp_die(__('Security check failed', 'orapi-content'));
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'orapi-content'));
        }

        $post_id = intval($_POST['post_id']);
        $custom_prompt = sanitize_textarea_field($_POST['custom_prompt']);

        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error(__('Post not found', 'orapi-content'));
        }

        // Check if user can edit this specific post
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error(__('You cannot edit this post', 'orapi-content'));
        }

        $result = $this->generate_content_for_post($post, $custom_prompt);

        if ($result['success']) {
            // Mark as generated
            update_post_meta($post_id, '_orapi_generated', current_time('mysql'));
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['data']);
        }
    }

    public function ajax_bulk_generate() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'orapi_bulk_nonce')) {
            wp_die(__('Security check failed', 'orapi-content'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'orapi-content'));
        }

        $post_id = intval($_POST['post_id']);
        $custom_prompt = sanitize_textarea_field($_POST['custom_prompt']);

        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error(__('Post not found', 'orapi-content'));
        }

        // Check if already processed
        if (get_post_meta($post_id, '_orapi_generated', true)) {
            wp_send_json_error(__('Post already has generated content', 'orapi-content'));
        }

        $result = $this->generate_content_for_post($post, $custom_prompt);

        if ($result['success']) {
            // Mark as generated
            update_post_meta($post_id, '_orapi_generated', current_time('mysql'));
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['data']);
        }
    }

    private function generate_content_for_post($post, $custom_prompt = '') {
        $options = get_option($this->option_name);

        // Get API key
        $api_key = $this->decrypt_api_key($options['api_key']);
        if (empty($api_key)) {
            return array('success' => false, 'data' => __('API key not configured', 'orapi-content'));
        }

        // Prepare prompt
        $prompt = !empty($custom_prompt) ? $custom_prompt : $options['prompt'];
        $prompt = str_replace('{{post_title}}', $post->post_title, $prompt);

        // Make API request
        $response = $this->call_openrouter_api($api_key, $options['model'], $prompt);

        if ($response['success']) {
            // Update post content
            $updated = wp_update_post(array(
                'ID' => $post->ID,
                'post_content' => $response['content']
            ));

            if ($updated && !is_wp_error($updated)) {
                return array('success' => true, 'data' => __('Content generated successfully', 'orapi-content'));
            } else {
                return array('success' => false, 'data' => __('Failed to update post', 'orapi-content'));
            }
        } else {
            return array('success' => false, 'data' => $response['error']);
        }
    }

    private function call_openrouter_api($api_key, $model, $prompt) {
        // Validate inputs
        if (empty($api_key) || empty($model) || empty($prompt)) {
            return array('success' => false, 'error' => __('Missing required parameters', 'orapi-content'));
        }

        // Validate prompt length (OpenRouter has limits)
        if (strlen($prompt) > 10000) {
            return array('success' => false, 'error' => __('Prompt too long (max 10,000 characters)', 'orapi-content'));
        }

        $url = 'https://openrouter.ai/api/v1/chat/completions';

        $headers = array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name'),
            'User-Agent' => 'WordPress/' . get_bloginfo('version') . '; ' . home_url()
        );

        $body = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => 2000,
            'temperature' => 0.7,
            'stream' => false
        );

        // Log the request for debugging (if WP_DEBUG is enabled)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('OpenRouter API Request: ' . wp_json_encode(array(
                'model' => $model,
                'prompt_length' => strlen($prompt),
                'timestamp' => current_time('mysql')
            )));
        }

        $response = wp_remote_post($url, array(
            'headers' => $headers,
            'body' => wp_json_encode($body),
            'timeout' => 60,
            'sslverify' => true,
            'user-agent' => 'WordPress/' . get_bloginfo('version') . '; OpenRouter-Content-Plugin/1.0.0'
        ));

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();

            // Log the error
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('OpenRouter API Error: ' . $error_message);
            }

            return array('success' => false, 'error' => $error_message);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        // Log response for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('OpenRouter API Response Code: ' . $response_code);
        }

        if ($response_code !== 200) {
            $error_data = json_decode($response_body, true);
            $error_message = 'API request failed';

            if (isset($error_data['error']['message'])) {
                $error_message = $error_data['error']['message'];
            } elseif (isset($error_data['error'])) {
                $error_message = is_string($error_data['error']) ? $error_data['error'] : 'API error occurred';
            }

            // Add response code to error message for debugging
            $error_message .= ' (HTTP ' . $response_code . ')';

            // Log the detailed error
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('OpenRouter API Error Details: ' . $response_body);
            }

            return array('success' => false, 'error' => $error_message);
        }

        $data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return array('success' => false, 'error' => __('Invalid JSON response from API', 'orapi-content'));
        }

        if (!isset($data['choices'][0]['message']['content'])) {
            // Log the unexpected response structure
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('OpenRouter API Unexpected Response: ' . $response_body);
            }

            return array('success' => false, 'error' => __('Invalid API response structure', 'orapi-content'));
        }

        $content = $data['choices'][0]['message']['content'];

        // Validate content length
        if (empty($content) || strlen($content) < 10) {
            return array('success' => false, 'error' => __('Generated content too short or empty', 'orapi-content'));
        }

        return array('success' => true, 'content' => $content);
    }
}

// Activation hook
register_activation_hook(__FILE__, 'orapi_content_activate');

function orapi_content_activate() {
    // Create default options
    $default_options = array(
        'api_key' => '',
        'model' => 'openai/gpt-3.5-turbo',
        'prompt' => 'Write a comprehensive blog post about {{post_title}}. Make it engaging and informative.',
        'wait_time' => 2
    );

    add_option('orapi_content_settings', $default_options);
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'orapi_content_deactivate');

function orapi_content_deactivate() {
    // Clean up scheduled events if any
    wp_clear_scheduled_hook('orapi_content_cleanup');
}

// Initialize the plugin
new OrapiContentGenerator();
