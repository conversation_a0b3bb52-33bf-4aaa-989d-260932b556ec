# OpenRouter API Content Generator
# Copyright (C) 2024
# This file is distributed under the same license as the OpenRouter API Content Generator package.
msgid ""
msgstr ""
"Project-Id-Version: OpenRouter API Content Generator 1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-16 12:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

msgid "OpenRouter API Content"
msgstr ""

msgid "OpenRouter API"
msgstr ""

msgid "Bulk Content Generator"
msgstr ""

msgid "OpenRouter API Settings"
msgstr ""

msgid "Configure your OpenRouter API settings below. The API key will be encrypted for security."
msgstr ""

msgid "API Key"
msgstr ""

msgid "Your OpenRouter API key (will be encrypted when saved)"
msgstr ""

msgid "Model"
msgstr ""

msgid "OpenRouter model to use (e.g., openai/gpt-3.5-turbo, anthropic/claude-3-haiku)"
msgstr ""

msgid "Default Prompt Template"
msgstr ""

msgid "Use {{post_title}} as a placeholder for the post title"
msgstr ""

msgid "API Wait Time (seconds)"
msgstr ""

msgid "Wait time between API calls to respect rate limits"
msgstr ""

msgid "Bulk Content Generation"
msgstr ""

msgid "Generate content for draft posts that don't have AI-generated content yet."
msgstr ""

msgid "Custom Prompt (optional):"
msgstr ""

msgid "Leave empty to use default prompt from settings. Use {{post_title}} for post title."
msgstr ""

msgid "Start Bulk Generation"
msgstr ""

msgid "Stop Generation"
msgstr ""

msgid "Progress"
msgstr ""

msgid "Posts to Process"
msgstr ""

msgid "No draft posts found that need content generation."
msgstr ""

msgid "Title"
msgstr ""

msgid "Date"
msgstr ""

msgid "Status"
msgstr ""

msgid "Pending"
msgstr ""

msgid "Processing..."
msgstr ""

msgid "Completed"
msgstr ""

msgid "Error"
msgstr ""

msgid "Security check failed"
msgstr ""

msgid "Insufficient permissions"
msgstr ""

msgid "Post not found"
msgstr ""

msgid "Post already has generated content"
msgstr ""

msgid "API key not configured"
msgstr ""

msgid "Content generated successfully"
msgstr ""

msgid "Failed to update post"
msgstr ""

msgid "Invalid API response"
msgstr ""

msgid "OpenRouter Content Generator"
msgstr ""

msgid "Content Generated:"
msgstr ""

msgid "Custom Prompt:"
msgstr ""

msgid "Leave empty to use default prompt. Use {{post_title}} for post title."
msgstr ""

msgid "Generate Content"
msgstr ""

msgid "Please configure your API settings first."
msgstr ""

msgid "Settings"
msgstr ""

msgid "Generating..."
msgstr ""

msgid "Generating content..."
msgstr ""

msgid "Content generated successfully!"
msgstr ""

msgid "Error:"
msgstr ""

msgid "An error occurred. Please try again."
msgstr ""

msgid "You cannot edit this post"
msgstr ""

msgid "Current Settings:"
msgstr ""

msgid "Model:"
msgstr ""

msgid "Wait Time:"
msgstr ""

msgid "seconds"
msgstr ""

msgid "API Key:"
msgstr ""

msgid "Configured"
msgstr ""

msgid "Not configured"
msgstr ""

msgid "Please configure your API settings before running bulk generation."
msgstr ""

msgid "Go to Settings"
msgstr ""

msgid "Enabled Post Types"
msgstr ""

msgid "Select post types to enable content generation"
msgstr ""

msgid "Select which post types should have content generation available"
msgstr ""

msgid "Post Type:"
msgstr ""

msgid "%s to Process"
msgstr ""

msgid "No draft %s found that need content generation."
msgstr ""

msgid "Missing required parameters"
msgstr ""

msgid "Prompt too long (max 10,000 characters)"
msgstr ""

msgid "Invalid JSON response from API"
msgstr ""

msgid "Invalid API response structure"
msgstr ""

msgid "Generated content too short or empty"
msgstr ""
