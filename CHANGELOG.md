# Changelog

All notable changes to the OpenRouter API Content Generator plugin will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-08-16

### Added
- Initial release of OpenRouter API Content Generator
- Secure API key storage with WordPress salt-based encryption
- Bulk content generation for draft posts
- Individual post content generation via meta box
- Configurable API settings (key, model, prompt template, wait time)
- Rate limiting to respect API limits
- Real-time progress tracking for bulk operations
- Post title variable substitution in prompts (`{{post_title}}`)
- Comprehensive security features:
  - Nonce verification for all AJAX requests
  - Capability checks for user permissions
  - Input sanitization and validation
  - SSL verification for API requests
- Admin interface with settings and bulk processing pages
- Responsive design for mobile compatibility
- Internationalization support (i18n ready)
- Uninstall script for clean removal
- Security hardening with .htaccess rules

### Security Features
- API keys encrypted using WordPress salts
- CSRF protection with nonces
- User capability verification
- Input sanitization and validation
- SQL injection prevention
- XSS protection
- Directory browsing disabled
- Direct file access prevention

### Technical Features
- WordPress coding standards compliance
- Proper error handling and logging
- AJAX-powered user interface
- Database optimization (minimal queries)
- Transient caching support (ready for future use)
- Hook system ready for extensions
- Clean uninstall process

### Supported Models
- OpenAI GPT models (gpt-3.5-turbo, gpt-4, etc.)
- Anthropic Claude models (claude-3-haiku, claude-3-sonnet, etc.)
- Any model available through OpenRouter API

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- OpenSSL extension for encryption
- Valid OpenRouter API account and key

### Known Limitations
- Only processes draft posts in bulk mode
- Requires manual refresh after individual generation
- No content preview before saving
- No undo functionality for generated content

### Future Enhancements (Planned)
- Content preview before saving
- Undo/revision support
- Custom post type support
- Scheduled content generation
- Content templates library
- Multi-language content generation
- Advanced prompt engineering tools
- Analytics and usage tracking
- Integration with popular page builders
