jQuery(document).ready(function($) {
    let isProcessing = false;
    let currentIndex = 0;
    let posts = [];
    let stopRequested = false;
    
    // Initialize posts array from table
    function initializePosts() {
        posts = [];
        $('.orapi-posts-list tbody tr').each(function() {
            posts.push({
                id: $(this).data('post-id'),
                title: $(this).find('td:first strong').text(),
                row: $(this)
            });
        });
    }
    
    // Update progress
    function updateProgress() {
        const total = posts.length;
        const processed = currentIndex;
        const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
        
        $('.progress-fill').css('width', percentage + '%');
        $('.progress-text').text(processed + ' / ' + total + ' posts processed');
    }
    
    // Update post status
    function updatePostStatus(postId, status, message = '') {
        const row = $('tr[data-post-id="' + postId + '"]');
        const statusCell = row.find('.status-cell');
        
        statusCell.html('<span class="status-' + status + '">' + 
            (message || orapi_ajax.strings[status] || status) + '</span>');
    }
    
    // Process single post
    function processSinglePost(post) {
        return new Promise((resolve, reject) => {
            updatePostStatus(post.id, 'processing', orapi_ajax.strings.processing);
            
            const customPrompt = $('#custom-prompt').val();
            
            $.ajax({
                url: orapi_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'orapi_bulk_generate',
                    post_id: post.id,
                    custom_prompt: customPrompt,
                    nonce: orapi_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updatePostStatus(post.id, 'completed', orapi_ajax.strings.completed);
                        resolve(response.data);
                    } else {
                        updatePostStatus(post.id, 'error', response.data || orapi_ajax.strings.error);
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    updatePostStatus(post.id, 'error', error || orapi_ajax.strings.error);
                    reject(error);
                }
            });
        });
    }
    
    // Wait function
    function wait(seconds) {
        return new Promise(resolve => setTimeout(resolve, seconds * 1000));
    }
    
    // Main bulk processing function
    async function processBulk() {
        if (posts.length === 0) {
            alert('No posts to process');
            return;
        }
        
        isProcessing = true;
        stopRequested = false;
        currentIndex = 0;
        
        // Show progress and update UI
        $('.orapi-progress').show();
        $('#start-bulk-generation').hide();
        $('#stop-bulk-generation').show();
        
        // Get wait time from WordPress options (we'll need to pass this from PHP)
        const waitTime = parseInt($('#wait-time-setting').val()) || 2;
        
        try {
            for (let i = 0; i < posts.length; i++) {
                if (stopRequested) {
                    console.log('Bulk processing stopped by user');
                    break;
                }
                
                currentIndex = i;
                updateProgress();
                
                try {
                    await processSinglePost(posts[i]);
                    console.log('Successfully processed post:', posts[i].title);
                } catch (error) {
                    console.error('Error processing post:', posts[i].title, error);
                    // Continue with next post even if one fails
                }
                
                // Wait between requests (except for the last one)
                if (i < posts.length - 1 && !stopRequested) {
                    console.log('Waiting', waitTime, 'seconds before next request...');
                    await wait(waitTime);
                }
            }
            
            currentIndex = posts.length;
            updateProgress();
            
            if (!stopRequested) {
                alert('Bulk processing completed!');
            }
            
        } catch (error) {
            console.error('Bulk processing error:', error);
            alert('An error occurred during bulk processing: ' + error);
        } finally {
            // Reset UI
            isProcessing = false;
            $('#start-bulk-generation').show();
            $('#stop-bulk-generation').hide();
        }
    }
    
    // Event handlers
    $('#start-bulk-generation').on('click', function() {
        if (isProcessing) {
            return;
        }
        
        if (!confirm('Are you sure you want to start bulk content generation? This will process all listed posts.')) {
            return;
        }
        
        initializePosts();
        processBulk();
    });
    
    $('#stop-bulk-generation').on('click', function() {
        if (!isProcessing) {
            return;
        }
        
        if (confirm('Are you sure you want to stop the bulk processing?')) {
            stopRequested = true;
            $(this).prop('disabled', true).text('Stopping...');
        }
    });
    
    // Initialize on page load
    initializePosts();
});
