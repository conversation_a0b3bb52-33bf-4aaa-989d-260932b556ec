<?php
/**
 * Uninstall script for OpenRouter API Content Generator
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It cleans up all plugin data from the database.
 */

// If uninstall not called from WordPress, exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Delete plugin options
delete_option('orapi_content_settings');

// Delete all post meta created by the plugin
global $wpdb;

$wpdb->delete(
    $wpdb->postmeta,
    array(
        'meta_key' => '_orapi_generated'
    )
);

// Clear any scheduled hooks (if any were added in future versions)
wp_clear_scheduled_hook('orapi_content_cleanup');

// Delete any transients (if any were used)
delete_transient('orapi_content_cache');

// Optional: Log the uninstall for debugging
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('OpenRouter API Content Generator plugin uninstalled and data cleaned up.');
}
