# OpenRouter API Content Generator

A secure WordPress plugin that generates post content using the OpenRouter API with bulk processing capabilities.

## Features

- **Secure API Key Storage**: API keys are encrypted using WordPress salts
- **Custom Post Type Support**: Works with any public post type (posts, pages, custom post types)
- **Bulk Content Generation**: Process multiple draft posts with rate limiting
- **Customizable Prompts**: Use `{{post_title}}` variable in prompts
- **Rate Limiting**: Configurable wait time between API calls
- **Security**: Nonce verification, capability checks, and input sanitization
- **Progress Tracking**: Real-time progress updates during bulk processing

## Installation

1. Upload the plugin files to `/wp-content/plugins/orapi-content/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure your settings in Settings > OpenRouter API

## Configuration

### Settings Page (Settings > OpenRouter API)

- **API Key**: Your OpenRouter API key (encrypted when saved)
- **Model**: OpenRouter model to use (e.g., `openai/gpt-3.5-turbo`, `anthropic/claude-3-haiku`)
- **Default Prompt Template**: Template for content generation (use `{{post_title}}` for post title)
- **API Wait Time**: Seconds to wait between API calls (minimum 1 second)
- **Enabled Post Types**: Select which post types should have content generation available

### Bulk Processing Page (Tools > Bulk Content Generator)

- View all draft posts without generated content
- Set custom prompts for bulk processing
- Monitor progress in real-time
- Stop processing at any time

## Usage

### Single Post Generation

The plugin automatically marks posts that have been processed to avoid duplicates.

### Bulk Generation

1. Go to Tools > Bulk Content Generator
2. Select the post type you want to process (if multiple are enabled)
3. Review the list of draft posts
4. Optionally set a custom prompt (leave empty to use default)
5. Click "Start Bulk Generation"
6. Monitor progress and stop if needed

### Custom Post Types

The plugin works with any public post type:
- Regular WordPress posts
- Pages
- Custom post types (products, events, etc.)
- Configure which post types are enabled in Settings > OpenRouter API

## Security Features

- **Encrypted API Keys**: Uses WordPress salts for encryption
- **Nonce Verification**: All AJAX requests are protected
- **Capability Checks**: Only users with `manage_options` can use the plugin
- **Input Sanitization**: All user inputs are properly sanitized
- **Rate Limiting**: Prevents API abuse with configurable delays

## API Integration

The plugin uses the OpenRouter API v1 chat completions endpoint:
- Endpoint: `https://openrouter.ai/api/v1/chat/completions`
- Authentication: Bearer token
- Timeout: 60 seconds
- SSL verification enabled

## Post Meta

The plugin adds the following meta fields to processed posts:
- `_orapi_generated`: Timestamp when content was generated

## Requirements

- WordPress 5.0+
- PHP 7.4+
- OpenSSL extension (for API key encryption)
- Valid OpenRouter API key

## Hooks and Filters

Currently, the plugin doesn't expose custom hooks, but this can be extended based on needs.

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify your OpenRouter API key is correct
   - Check if you have sufficient credits
   - Ensure the model you selected is available

2. **Bulk Processing Stops**
   - Check for PHP timeout limits
   - Verify API rate limits aren't exceeded
   - Check WordPress error logs

3. **Content Not Generated**
   - Ensure posts are in "draft" status
   - Check if posts already have the `_orapi_generated` meta field
   - Verify API settings are configured

### Debug Mode

To enable debug mode, add this to your `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Changelog

### 1.0.0
- Initial release
- Secure API key storage
- Bulk content generation
- Rate limiting
- Progress tracking

## License

GPL v2 or later

## Support

For support, please check the WordPress plugin directory or contact the plugin author.
