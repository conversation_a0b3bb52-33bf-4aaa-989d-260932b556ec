# Security rules for OpenRouter API Content Generator plugin

# Deny access to sensitive files
<Files "*.log">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.pot">
    Order allow,deny
    Den<PERSON> from all
</Files>

<Files "README.md">
    Order allow,deny
    Deny from all
</Files>

<Files "uninstall.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent direct access to PHP files (except main plugin file)
<FilesMatch "^(?!orapi-content\.php$).*\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Protect against common attacks
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block suspicious requests
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC]
    RewriteRule .* - [F]
</IfModule>
