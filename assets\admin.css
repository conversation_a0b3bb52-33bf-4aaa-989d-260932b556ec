/* OpenRouter API Content Generator Admin Styles */

.orapi-bulk-controls {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.orapi-settings-info {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-left: 4px solid #0073aa;
}

.orapi-settings-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.orapi-settings-info li {
    margin: 5px 0;
}

.orapi-custom-prompt {
    margin: 15px 0;
}

.orapi-custom-prompt label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.orapi-progress {
    margin: 20px 0;
    padding: 15px;
    background: #f1f1f1;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #ddd;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    margin-top: 10px;
    font-weight: bold;
    text-align: center;
}

.orapi-posts-list {
    margin-top: 20px;
}

.orapi-posts-list h4 {
    margin-bottom: 15px;
    color: #23282d;
}

/* Status indicators */
.status-pending {
    color: #666;
    font-style: italic;
}

.status-processing {
    color: #0073aa;
    font-weight: bold;
}

.status-processing::before {
    content: "⏳ ";
}

.status-completed {
    color: #46b450;
    font-weight: bold;
}

.status-completed::before {
    content: "✅ ";
}

.status-error {
    color: #dc3232;
    font-weight: bold;
}

.status-error::before {
    content: "❌ ";
}

/* Button styles */
#start-bulk-generation {
    margin-right: 10px;
}

#start-bulk-generation:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#stop-bulk-generation {
    background: #dc3232;
    border-color: #dc3232;
    color: #fff;
}

#stop-bulk-generation:hover {
    background: #c62d2d;
    border-color: #c62d2d;
}

/* Settings page styles */
.orapi-settings-section {
    max-width: 800px;
}

.orapi-settings-section .form-table th {
    width: 200px;
}

.orapi-settings-section .form-table td {
    padding: 15px 10px;
}

.orapi-settings-section .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
    .orapi-bulk-controls {
        padding: 15px;
    }
    
    .orapi-custom-prompt textarea {
        width: 100%;
    }
    
    .orapi-posts-list table {
        font-size: 14px;
    }
    
    .orapi-posts-list th,
    .orapi-posts-list td {
        padding: 8px 4px;
    }
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-processing {
    animation: pulse 1.5s infinite;
}

/* Notice styles */
.orapi-notice {
    margin: 15px 0;
    padding: 12px;
    border-left: 4px solid #0073aa;
    background: #fff;
}

.orapi-notice.error {
    border-left-color: #dc3232;
}

.orapi-notice.success {
    border-left-color: #46b450;
}

.orapi-notice.warning {
    border-left-color: #ffb900;
}
